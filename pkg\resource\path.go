package resource

import (
	"fmt"
	"resource-manager/pkg/db"
)

const Path = "path"

func init() {
	RegisterResource(Path, func(db *db.DB) Resource {
		return &PathResource{
			db:    db,
			table: "range_edu.learning_paths",
		}
	})
}

type PathResource struct {
	db    *db.DB
	table string
}

func (r *PathResource) GetCodes(name string) ([]string, error) {
	return r.db.GetCodesByName(name, r.table)
}

func (r *PathResource) GetRelatedCodes(code string) (map[string][]string, error) {
	// 无向外关联
	return nil, nil
}

func (r *PathResource) GetMetaDir(code, path string) (any, error) {
	return "not implemented", nil
}

func (r *PathResource) Import(path string) error {
	return fmt.Errorf("not implemented, %s", path)
}

func (r *PathResource) Export(codes []string) error {
	return fmt.Errorf("not implemented, %s", outputTmpDir)
}
