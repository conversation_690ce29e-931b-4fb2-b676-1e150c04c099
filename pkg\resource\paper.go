package resource

import (
	"fmt"
	"resource-manager/pkg/db"
)

const Paper = "paper"

func init() {
	RegisterResource(Paper, func(db *db.DB) Resource {
		return &PaperResource{
			db:    db,
			table: "range_exam.papers",
		}
	})
}

type PaperResource struct {
	db    *db.DB
	table string
}

func (r *PaperResource) GetCodes(name string) ([]string, error) {
	return r.db.GetCodesByName(name, r.table)
}

func (r *PaperResource) GetRelatedCodes(code string) (map[string][]string, error) {
	// 无向外关联
	return nil, nil
}

func (r *PaperResource) GetMetaDir(code, path string) (any, error) {
	return "not implemented", nil
}

func (r *PaperResource) Import(path string) error {
	return fmt.Errorf("not implemented, %s", path)
}

func (r *PaperResource) Export(codes []string) error {
	return fmt.Errorf("not implemented, %s", outputTmpDir)
}
