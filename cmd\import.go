package cmd

import (
	"github.com/spf13/cobra"
)

func init() {
	RootCmd.AddCommand(importCmd)

	importCmd.Flags().StringP("dir", "d", "", "directory to import")
	importCmd.Flags().StringP("type", "t", "", "type of resources to import")
	importCmd.Flags().StringP("code", "c", "", "code of the resources to import")
	importCmd.Flags().StringP("key", "k", "", "decryption key string")
}

var importCmd = &cobra.Command{
	Use:   "import",
	Short: "Import resources",
	Run: func(cmd *cobra.Command, args []string) {
		// dir, _ := cmd.Flags().GetString("dir")
		// keyStr, _ := cmd.Flags().GetString("key")

		// if dir == "" {
		// 	return fmt.Errorf("file is required")
		// }

		// // 创建数据库连接
		// db, err := db.NewDB("user:password@tcp(localhost:3306)/resource_db")
		// if err != nil {
		// 	return fmt.Errorf("failed to connect to database: %v", err)
		// }

		// // 读取资源包信息
		// packageInfo, err := meta.ReadPackageInfo(file)
		// if err != nil {
		// 	return fmt.Errorf("failed to read package info: %v", err)
		// }

		// // 生成解密密钥
		// var key []byte
		// if keyStr != "" {
		// 	key = crypto.GenerateKey(keyStr)
		// }

		// // 导入资源
		// for _, res := range packageInfo.Resources {
		// 	resDir := filepath.Join(file, res.Code)

		// 	// 读取元数据
		// 	if key != nil {
		// 		_, err = meta.ReadEncryptedMeta(filepath.Join(resDir, "meta.yaml"), key)
		// 	} else {
		// 		_, err = meta.ReadMeta(filepath.Join(resDir, "meta.yaml"))
		// 	}
		// 	if err != nil {
		// 		return fmt.Errorf("failed to read meta data for resource %s: %v", res.Code, err)
		// 	}

		// 	// 导入资源内容
		// 	if err := importResource(res, resDir); err != nil {
		// 		return fmt.Errorf("failed to import resource %s: %v", res.Code, err)
		// 	}

		// 	// 创建资源关联关系
		// 	if res.Code != packageInfo.Code {
		// 		if err := db.CreateResourceRelation(
		// 			resource.ResourceType(packageInfo.Code),
		// 			packageInfo.Code,
		// 			resource.ResourceType(res.Code),
		// 			res.Code,
		// 		); err != nil {
		// 			return fmt.Errorf("failed to create resource relation: %v", err)
		// 		}
		// 	}
		// }

		// return nil
	},
}
