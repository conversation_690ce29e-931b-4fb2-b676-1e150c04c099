package cmd

import (
	"resource-manager/internal/info"
	"resource-manager/pkg/resource"
)

// initResource 初始化资源中的特殊项
// isDB表示工厂初始化时是否有有效的db用于数据库查询
// 多code无code等情况跳过初始化
func initResource(fct resource.Resource, rscInfo *info.Info, isDB bool) error {
	switch fct := fct.(type) {
	case *resource.SceneResource:
		if isDB {
			// 用code查关系
			if err := fct.InitVersions(rscInfo.Code, nil); err != nil {
				return err
			}
		} else {
			// 已有关系
			if rscInfo.Children[resource.Version] != nil {
				// Versions为空则赋为空切片
				codes := append([]string(nil), rscInfo.Children[resource.Version]...)
				if err := fct.InitVersions("", codes); err != nil {
					return err
				}
			}
		}
	case *resource.CorpusResource:
		switch rscInfo.Type {
		case resource.CTF, resource.AWD, resource.BTC:
			if err := fct.InitType("", rscInfo.Type); err != nil {
				return err
			}
		default:
			// 用code查
			if err := fct.InitType(rscInfo.Code, ""); err != nil {
				return err
			}
		}
	default:
		return nil
	}
	return nil
}
