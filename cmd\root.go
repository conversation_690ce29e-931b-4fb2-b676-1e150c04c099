package cmd

import (
	"github.com/spf13/cobra"

	"resource-manager/internal/logger"
)

var logLevel string

func init() {
	RootCmd.PersistentFlags().StringVarP(&logLevel, "log-level", "l", "INFO", "log level (PANIC|FATAL|ERROR|WARN|INFO|DEBUG|TRACE)")
}

var RootCmd = &cobra.Command{
	Use: "",
	PersistentPreRun: func(cmd *cobra.Command, args []string) {
		// 初始化日志设置
		logger.InitLogger(logLevel)
	},
	Run: func(cmd *cobra.Command, args []string) {
		_ = cmd.Help()
	},
}
