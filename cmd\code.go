package cmd

import (
	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
)

func init() {
	RootCmd.AddCommand(codeCmd)

	codeCmd.Flags().StringP("name", "n", "", "name of resource")
	codeCmd.Flags().StringP("type", "t", "", "type of resource")
}

var codeCmd = &cobra.Command{
	Use:   "get-code",
	Short: "",
	Run: func(cmd *cobra.Command, args []string) {
		name, _ := cmd.Flags().GetString("name")
		rscType, _ := cmd.Flags().GetString("type")

		if name == "" || rscType == "" {
			logrus.Fatal("Please specify name and reousrce type!")
		}

		// 连接数据库

		// 查找

		// 规范输出
	},
}
