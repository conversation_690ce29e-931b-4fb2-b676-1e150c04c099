package resource

import (
	"fmt"
	"path/filepath"
	"strings"

	"resource-manager/pkg/db"
)

const (
	Corpus = "corpus"
	CTF    = "CTF"
	AWD    = "AWD"
	BTC    = "BTC"
)

func init() {
	RegisterResource(Corpus, func(db *db.DB) Resource {
		return &CorpusResource{
			db: db,
		}
	})
}

type CorpusResource struct {
	db    *db.DB
	cType string // 赛题类型，大写，CTF、AWD、BTC
}

func (r *CorpusResource) GetCodes(name string) ([]string, error) {
	return r.db.GetCodesByName(name, tableCorpus)
}

func (r *CorpusResource) GetRelatedCodes(code string) (map[string][]string, error) {
	relation, err := r.db.QueryMulti(fmt.Sprintf("SELECT relation_id, relation_type FROM %s WHERE `code` = ?", tableCorpus), code)
	// relation应有且只有一行（code唯一），0为id、1为type
	if err != nil || len(relation) == 0 {
		return nil, fmt.Errorf("failed to get relation: %v", err)
	}
	if relation[0][1] == "scene" {
		// 场景，版本
		versionFullCode, err := r.db.QueryRow(fmt.Sprintf("SELECT full_code FROM %s WHERE id = ?", tableSceneVersion), relation[0][0])
		if err != nil {
			return nil, fmt.Errorf("failed to get relate scene version code: %s", err)
		}

		res := make(map[string][]string)
		res["version"] = []string{versionFullCode}
		// 获取版本对应镜像code
		imageCodes, err := GetImageByVersion(r.db, relation[0][0])
		if err != nil {
			return nil, fmt.Errorf("failed to get relate image code: %s", err)
		}
		res[fmt.Sprintf("mirror-%s", versionFullCode)] = imageCodes
		return res, nil

	} else if relation[0][1] == "resource" {
		// 镜像
		mirrorCode, err := r.db.QueryRow(fmt.Sprintf("SELECT code FROM %s WHERE id = ?", tableMirror), relation[0][0])
		return map[string][]string{"mirror": {mirrorCode}}, err
	}

	return nil, nil
}

func (r *CorpusResource) GetMetaDir(code, path string) (any, error) {
	if r.cType == "" {
		if err := r.InitType(code, ""); err != nil {
			return nil, err
		}
	}
	return GetMetaDir(filepath.Join(path, Corpus), fmt.Sprintf(`^%s-.*-%s`, r.cType, code))
}

func (r *CorpusResource) Import(path string) error {
	return postRequest(fmt.Sprintf(`%s:8080/slab-match/api/v1/corpus/init?path=%s`, serviceMatch, filepath.Join(path, Corpus)), "", nil)
}

func (r *CorpusResource) Export(codes []string) error {
	return postRequest(fmt.Sprintf(`%s:8080/slab-match/api/v1/corpus/%s/export-corpus-inner`, serviceMatch, strings.ToLower(r.cType)), filepath.Join(outputTmpDir, Corpus), codes)
}

/*** 类型辅助函数 ***/

// InitType 初始化赛题类型，覆盖已有
// 优先给定类型，否则code查数据库，db非必需
func (r *CorpusResource) InitType(code, cType string) error {
	if cType == "" {
		var err error
		cType, err = r.db.QueryRow(fmt.Sprintf("SELECT type from %s WHERE `code` = ?", tableCorpus), code)
		if err != nil {
			return fmt.Errorf("failed to get corpus type: %v", err)
		}
	}
	r.cType = strings.ToUpper(cType)
	return nil
}
