package resource

import (
	"fmt"
	"resource-manager/pkg/db"
)

const Knowledge = "knowledge"

func init() {
	RegisterResource(Knowledge, func(db *db.DB) Resource {
		return &KnowledgeResource{
			db:    db,
			table: "resource_management.knowledge",
		}
	})
}

type KnowledgeResource struct {
	db    *db.DB
	table string
}

func (r *KnowledgeResource) GetCodes(name string) ([]string, error) {
	return r.db.GetCodesByName(name, r.table)
}

func (r *KnowledgeResource) GetRelatedCodes(code string) (map[string][]string, error) {
	// 无向外关联
	return nil, nil
}

func (r *KnowledgeResource) GetMetaDir(code, path string) (any, error) {
	return "not implemented", nil
}

func (r *KnowledgeResource) Import(path string) error {
	return fmt.Errorf("not implemented, %s", path)
}

func (r *KnowledgeResource) Export(codes []string) error {
	return fmt.Errorf("not implemented, %s", outputTmpDir)
}
