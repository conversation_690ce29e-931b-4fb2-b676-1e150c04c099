package cmd

import (
	"os"
	"path/filepath"
	"resource-manager/pkg/crypto"
	"resource-manager/pkg/db"
	"resource-manager/pkg/resource"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
)

func init() {
	RootCmd.AddCommand(exportCmd)

	exportCmd.Flags().StringP("dir", "d", "/data/resource/", "directory to export")
	exportCmd.Flags().StringP("type", "t", "", "type of resources to export")
	exportCmd.Flags().StringP("codes", "c", "", "codes of the resources to export")
	exportCmd.Flags().BoolP("self", "s", false, "only current resource itself without associations, not support encrypt")
	exportCmd.Flags().BoolP("plain", "", false, "export without encryption")
	exportCmd.Flags().StringP("key", "k", "", "encryption key string")
}

var exportCmd = &cobra.Command{
	Use:   "export",
	Short: "Export resources",
	Run: func(cmd *cobra.Command, args []string) {
		dir, _ := cmd.Flags().GetString("dir")
		rscType, _ := cmd.Flags().GetString("type")
		codes, _ := cmd.Flags().GetString("codes")
		self, _ := cmd.Flags().GetBool("self")
		plain, _ := cmd.Flags().GetBool("plain")
		keyStr, _ := cmd.Flags().GetString("key")

		if rscType == "" || codes == "" {
			logrus.Fatal("Please specify resource type and code!")
		}

		if _, err := os.Stat(dir); os.IsNotExist(err) {
			if err := os.MkdirAll(dir, 0755); err != nil {
				logrus.Fatalf("failed to create directory: %v", err)
			}
		}

		logrus.Info("Exporting resources ...")

		// 分割多个codes
		codesList := strings.Split(codes, ",")
		if len(codesList) == 0 {
			logrus.Fatal("Please specify resource code!")
		}

		if self {
			exportSelfResource(rscType, dir, codesList)
		} else {
			exportRelatedResource(rscType, dir, codesList)
		}

		// for _, code := range codesList {
		// 	// 创建导出目录
		// 	exportDir := filepath.Join(path, fmt.Sprintf("%s%s-%s", selfFlag, code, rscType))
		// 	if err := os.MkdirAll(exportDir, 0755); err != nil {
		// 		logrus.Fatalf("failed to create export directory: %v", err)
		// 	}

		// 	if self {
		// 		// 单个资源导出
		// 		logrus.Info("Exporting single resource ...")
		// 		if err := exportSelfResource(rscType, codes, path); err != nil {
		// 			logrus.Errorf("failed to export single resource: %v", err)
		// 		}
		// 	} else {
		// 		// 关联资源导出
		// 		logrus.Info("Exporting related resources ...")
		// 		if err := exportRelatedResource(rscType, codes, path); err != nil {
		// 			logrus.Errorf("failed to export related resources: %v", err)
		// 		}
		// 	}
		// }

		// 加密
		if !plain {
			var key []byte
			if keyStr == "" {
				key = crypto.GenerateKey(keyStr)
			} else {
				key = []byte(keyStr)
			}

			// 加密meta文件
			logrus.Infof("key: %v", key)
		}

	},
}

// exportSelfResource 导出指定资源本身，不支持加密
func exportSelfResource(rscType, path string, code []string) {
	path = filepath.Join(path, time.Now().Format("20060102150405"))
	// 加载工厂
	fct, err := resource.GetFactory(rscType, nil)
	if err != nil {
		logrus.Fatalf("failed to get resource: %v", err)
	}

	if err := fct.Export(code); err != nil {
		logrus.Fatalf("failed to export resource: %v", err)
	}
	logrus.Infof("resources exported successfully in %v", path)
}

// exportRelatedResource 导出相关资源
func exportRelatedResource(rscType, path string, code []string) {
	// 创建数据库连接
	dbConf, err := db.GetEnv()
	if err != nil {
		logrus.Errorf("failed to get db from env: %v", err)
		logrus.Debugf("Try config file???")
		return
	}
	db, err := db.NewDB(db.GetDSN(dbConf))
	if err != nil {
		logrus.Fatalf("failed to connect to db: %v", err)
	}
	defer db.Close()

}
