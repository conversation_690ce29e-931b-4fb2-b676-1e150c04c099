package resource

import (
	"encoding/json"
	"fmt"
	"os/exec"
	"strings"

	"github.com/sirupsen/logrus"
)

const (
	// 表名
	tableMirror       = "slab_resource.mirrors"
	tableScene        = "slab_resource.scenes"
	tableSceneVersion = "slab_resource.scene_versions"
	tableSceneDevice  = "slab_resource.scene_devices"
	tableCorpus       = "match_center.corpus"

	// 服务名称
	serviceSlabResource = "slab-resource"
	serviceMatch        = "match"

	// 容器名称
	containerGatewayInner = "gateway-inner"

	// 临时导出目录
	outputTmpDir = "/resource-manager-tmp/"
)

var fileType = []string{"pdf", "zip", "mp4", "xlsx"} // 需加密文件类型

/*** 导入导出辅助工具函数 ***/
// postRequest 在容器中发送导入导出Post请求，导出附带codes请求体
func postRequest(url, path string, codes []string) error {
	// 构建请求体
	var requestBody string
	if codes != nil {
		body := struct {
			Type  string   `json:"type"`
			Codes []string `json:"codes"`
			Path  string   `json:"path"`
		}{
			Type:  "codes",
			Codes: codes,
			Path:  path,
		}
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return fmt.Errorf("failed to marshal body: %v", err)
		}
		requestBody = string(jsonBody)
	}

	// 构建命令
	curlCmd := fmt.Sprintf(`curl -H "Content-Type: application/json" -X POST "%s" -d '%s'`, url, requestBody)
	cmd := fmt.Sprintf(`docker exec -i %s sh -c "%s"`, containerGatewayInner, strings.ReplaceAll(curlCmd, `"`, `\"`))

	// 执行命令
	output, err := exec.Command("sh", "-c", cmd).CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to execute request: %v, output: %s", err, string(output))
	}

	logrus.Debugf("Post Request Output: %s", string(output))
	return nil
}
