# Mac OS X files
.DS_Store

# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, build with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Project-local glide cache, RE: https://github.com/Masterminds/glide/issues/736
.glide/

# Dependency directories (remove the comment below to include it)
# vendor/

.token

# log
log/

.idea
.vscode

go.sum
demo/
test/

vv*

