package resource

import (
	"fmt"
	"path/filepath"

	"resource-manager/pkg/db"
)

const Mirror = "mirror"

func init() {
	RegisterResource(Mirror, func(db *db.DB) Resource {
		return &MirrorResource{
			db: db,
		}
	})
}

type MirrorResource struct {
	db *db.DB
}

func (r *MirrorResource) GetCodes(name string) ([]string, error) {
	return r.db.GetCodesByName(name, tableMirror)
}

func (r *MirrorResource) GetRelatedCodes(code string) (map[string][]string, error) {
	// 无向外关联
	return nil, nil
}

func (r *MirrorResource) GetMetaDir(code, path string) (any, error) {
	return GetMetaDir(filepath.Join(path, Mirror), fmt.Sprintf(`^Matrix-.*-%s`, code))
}

func (r *MirrorResource) Import(path string) error {
	return postRequest(fmt.Sprintf(`http://%s:8080/slab-image/api/inner/mirror/import-path?path=%s`, serviceSlabResource, filepath.Join(path, Mirror)), "", nil)
}

func (r *MirrorResource) Export(codes []string) error {
	return postRequest(fmt.Sprintf(`http://%s:8080/slab-image/api/inner/mirror/export-path`, serviceSlabResource), filepath.Join(outputTmpDir, Mirror), codes)
}
