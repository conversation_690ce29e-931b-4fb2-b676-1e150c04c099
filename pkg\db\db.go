package db

import (
	"database/sql"
	"fmt"
	"os"

	_ "github.com/go-sql-driver/mysql"
	"github.com/joho/godotenv"

	"resource-manager/internal/config"
)

// DB MySQL数据库连接
type DB struct {
	*sql.DB
}

// NewDB 创建新的数据库连接
func NewDB(dsn string) (*DB, error) {
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, err
	}

	if err := db.Ping(); err != nil {
		return nil, err
	}

	return &DB{db}, nil
}

// GetEnv 从系统获取数据库配置
func GetEnv() (*config.DB, error) {
	err := godotenv.Load("/csr/.env")
	if err != nil {
		return nil, fmt.Errorf("failed to load .env: %v", err)
	} else {
		return &config.DB{
			Host:     "localhost",
			Port:     3306,
			User:     os.Getenv("MYSQL_USER"),
			Password: os.Getenv("MYSQL_PASSWORD"),
			DBName:   os.Getenv("MYSQL_DATABASE"),
		}, nil
	}
}

// GetDSN 根据配置构造DSN
func GetDSN(db *config.DB) string {
	return fmt.Sprintf("%s:%s@tcp(%s::%d)/%s?charset=utf8mb4&parseTime=True&loc=Local", db.User, db.Password, db.Host, db.Port, db.DBName)
}

// GetCodesByName 获取指定表中指定name的codes
func (db *DB) GetCodesByName(name, table string) ([]string, error) {
	return db.Query(fmt.Sprintf("SELECT `code` FROM %s WHERE name = ?", table), name)
}

/*** 查询结果封装string ***/

// Query 执行SQL查询并返回结果集，仅支持返回一列
func (db *DB) Query(query string, args ...any) ([]string, error) {
	rows, err := db.DB.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query: %v", err)
	}
	defer rows.Close()

	var res []string
	for rows.Next() {
		var tmp string
		if err := rows.Scan(&tmp); err != nil {
			return nil, fmt.Errorf("failed to get query result: %v", err)
		}
		res = append(res, tmp)
	}
	return res, nil
}

// QueryRow 执行SQL查询返回单条结果，仅支持返回一列
func (db *DB) QueryRow(query string, args ...any) (string, error) {
	var res string
	err := db.DB.QueryRow(query, args...).Scan(&res)
	if err != nil {
		return "", err
	}
	return res, nil
}

// QueryMulti 执行SQL查询并返回多列结果集
// 返回一个二维字符串切片，其中每个内部切片代表一行数据
func (db *DB) QueryMulti(query string, args ...any) ([][]string, error) {
	rows, err := db.DB.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query: %v", err)
	}
	defer rows.Close()

	// 获取列信息
	columns, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("failed to get columns: %v", err)
	}

	// 准备结果集
	var result [][]string
	// 准备扫描用的切片
	values := make([]any, len(columns))
	valuePtrs := make([]any, len(columns))
	for i := range columns {
		valuePtrs[i] = &values[i]
	}

	// 遍历结果集
	for rows.Next() {
		// 扫描当前行到values切片
		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, fmt.Errorf("failed to scan row: %v", err)
		}

		// 将当前行转换为字符串切片
		row := make([]string, len(columns))
		for i, v := range values {
			switch val := v.(type) {
			case nil:
				row[i] = ""
			case []byte:
				row[i] = string(val)
			case string:
				row[i] = val
			case int64:
				row[i] = fmt.Sprintf("%d", val)
			case float64:
				row[i] = fmt.Sprintf("%f", val)
			case bool:
				row[i] = fmt.Sprintf("%t", val)
			default:
				row[i] = fmt.Sprintf("%v", val)
			}
		}
		result = append(result, row)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating rows: %v", err)
	}

	return result, nil
}
