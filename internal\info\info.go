package info

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v3"
)

// Info 用于辅助加解密
type Info struct {
	// Type     string              `yaml:"type"`
	// Code     string              `yaml:"code"`
	// Name     string              `yaml:"name"`
	// Children map[string][]string `yaml:"children,omitempty"` // map[type]codes
	// Relation *Relation           `yaml:"relation,omitempty"`
	MetaDir map[string]string `yaml:"metaDir,omitempty"` // map[id]dir
	FileDir []string          `yaml:"fileDir,omitempty"` // 被加密资源文件目录
}

// // Relation 子资源，所有直接和间接相关子资源的关联关系
// type Relation struct {
// 	Mirror    []*RelateItem `yaml:"mirror,omitempty"`
// 	Scene     []*RelateItem `yaml:"scene,omitempty"`
// 	Version   []*RelateItem `yaml:"version,omitempty"`
// 	Tool      []*RelateItem `yaml:"tool,omitempty"`
// 	Knowledge []*RelateItem `yaml:"knowledge,omitempty"`
// 	Course    []*RelateItem `yaml:"course,omitempty"`
// 	Stem      []*RelateItem `yaml:"stem,omitempty"`
// }

// // RelateItem 子资源项，记录和下层子资源的关系
// type RelateItem struct {
// 	Code     string              `yaml:"code"`
// 	Children map[string][]string `yaml:"children,omitempty"` // map[type]codes
// 	// Aux      any                 `yaml:"aux,omitempty"`
// }

// ReadInfo 解析文件获得info
func ReadInfo(file string) (*Info, error) {
	ctt, err := os.ReadFile(file)
	if err != nil {
		return nil, fmt.Errorf("failed to read info file: %v", err)
	}

	var info Info
	if err := yaml.Unmarshal(ctt, &info); err != nil {
		return nil, fmt.Errorf("failed to parse info file: %v", err)
	}
	return &info, nil
}

// WriteInfo 将Info写入文件，覆盖文件内容
func WriteInfo(file string, info *Info) error {
	data, err := yaml.Marshal(info)
	if err != nil {
		return err
	}

	err = os.WriteFile(file, data, 0644)
	if err != nil {
		return err
	}

	return nil
}
