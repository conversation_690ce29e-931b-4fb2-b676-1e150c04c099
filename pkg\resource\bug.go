package resource

import (
	"fmt"
	"resource-manager/pkg/db"
)

const Bug = "bug"

func init() {
	RegisterResource(Bug, func(db *db.DB) Resource {
		return &BugResource{
			db:    db,
			table: "resource_management.bug",
		}
	})
}

type BugResource struct {
	db    *db.DB
	table string
}

func (r *BugResource) GetCodes(name string) ([]string, error) {
	return r.db.GetCodesByName(name, r.table)
}

func (r *BugResource) GetRelatedCodes(code string) (map[string][]string, error) {
	// 无向外关联
	return nil, nil
}

func (r *BugResource) GetMetaDir(code, path string) (any, error) {
	return "not implemented", nil
}

func (r *BugResource) Import(path string) error {
	return fmt.Errorf("not implemented, %s", path)
}

func (r *BugResource) Export(codes []string) error {
	return fmt.Errorf("not implemented, %s", outputTmpDir)
}
