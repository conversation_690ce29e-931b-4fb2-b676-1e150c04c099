package meta

const Filename = "meta.yaml"

// Meta meta.yaml文件结构
type Meta struct {
	Code     string `yaml:"code"`
	Name     string `yaml:"name"`
	FullCode string `yaml:"fullCode"`

	// 未使用
	Type                string   `yaml:"type"`
	Description         string   `yaml:"description"`
	Driver              string   `yaml:"driver"`
	IconCode            string   `yaml:"iconCode"`
	InPermit            bool     `yaml:"inPermit"`
	Iource              string   `yaml:"source"`
	Tags                []string `yaml:"tags"`
	ExportPath          string   `yaml:"exportPath"`
	Config              string   `yaml:"config"`       // struct
	GroupAllInfo        string   `yaml:"groupAllInfo"` // struct
	Layou               string   `yaml:"layout"`       // struct
	NodeMap             string   `yaml:"nodeMap"`      // []struct
	Nodes               string   `yaml:"nodes"`
	SupportVirtualImage bool     `yaml:"supportVirtualImage"`
	TopologyGroups      string   `yaml:"topologyGroups"`   // struct
	TopologySnapshot    string   `yaml:"topologySnapshot"` // struct
	Url                 string   `yaml:"url"`
}
