package resource

import (
	"fmt"
	"strings"

	"resource-manager/pkg/db"
)

var factories map[string]Factory

func init() {
	factories = make(map[string]Factory)
}

// Factory 工厂函数类型，接收数据库连接并返回资源工厂实例
type Factory func(db *db.DB) Resource

// RegisterResource 注册资源工厂
func RegisterResource(name string, factory Factory) {
	factories[name] = factory
}

// Resource 资源工厂接口
type Resource interface {
	GetCodes(name string) ([]string, error)                   // 根据name获取可能存在的code，需db
	GetRelatedCodes(code string) (map[string][]string, error) //获取相关资源及code，map[type][codes]，需db
	GetMetaDir(code, path string) (any, error)                // 根据导出路径获得meta文件绝对路径，路径自动添加类型，返回string或map[string]string
	Import(path string) error                                 // 导入指定路径下资源s，路径自动添加类型
	Export(codes []string) error                              // 导出指定资源s到路径，路径自动添加类型
}

// GetFactory 获取资源工厂实例
func GetFactory(resourceType string, db *db.DB) (Resource, error) {
	resourceType = strings.ToLower(resourceType)
	switch resourceType {
	case CTF, AWD, BTC:
		return factories[Corpus](db), nil
	default:
		if factory, ok := factories[resourceType]; ok {
			return factory(db), nil
		}

	}

	return nil, fmt.Errorf("no resource instance found: %v", resourceType)
}
