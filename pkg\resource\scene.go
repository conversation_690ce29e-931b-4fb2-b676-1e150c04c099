package resource

import (
	"fmt"
	"path/filepath"

	"github.com/sirupsen/logrus"

	"resource-manager/pkg/db"
)

const (
	Scene   = "scene"
	Version = "version"
)

func init() {
	RegisterResource(Scene, func(db *db.DB) Resource {
		return &SceneResource{
			db: db,
		}
	})
}

type SceneResource struct {
	db       *db.DB
	versions [][]string // 0为id，1为full_code，2为name
}

func (r *SceneResource) GetCodes(name string) ([]string, error) {
	// 查scene
	codes, err := r.db.GetCodesByName(name, tableScene)
	if err != nil {
		return nil, fmt.Errorf("failed to get codes in [scene] by %s: %v", name, err)
	}

	if codes == nil {
		logrus.Debugf("%s not found in [scene], try in [scene_versions]", name)
		// 查version然后返回scene
		sceneIDs, err := r.db.Query(fmt.Sprintf("SELECT DISTINCT scene_id FROM %s WHERE name = ?", tableSceneVersion), name)
		if err != nil {
			return nil, fmt.Errorf("failed to get scene ids in [scene_versions] by %s: %v", name, err)
		}
		codes = []string{}
		for _, id := range sceneIDs {
			if tmp, err := r.db.QueryRow(fmt.Sprintf("SELECT `code` FROM %s WHERE ID = ?", tableScene), id); err != nil {
				return nil, fmt.Errorf("failed to get code in [scene]: %v", err)
			} else {
				codes = append(codes, tmp)
			}
		}
	}

	return codes, nil
}

func (r *SceneResource) GetRelatedCodes(code string) (map[string][]string, error) {
	if r.versions == nil {
		if err := r.InitVersions(code, nil); err != nil {
			return nil, err
		}
	}

	if r.versions == nil {
		// 无关联版本
		return nil, nil
	} else {
		// 版本关联镜像
		res := make(map[string][]string)
		for _, ver := range r.versions {
			codes, err := GetImageByVersion(r.db, ver[0])
			if err != nil {
				return nil, fmt.Errorf("failed to get image by version: %v", err)
			}
			res[fmt.Sprintf("mirror-%s", ver[1])] = codes
		}
		return res, nil
	}
}

func (r *SceneResource) GetMetaDir(code, path string) (any, error) {
	res := make(map[string]string)
	// 场景meta
	sceneMeta, err := GetMetaDir(filepath.Join(path, Scene), fmt.Sprintf(`^场景-.*-%s`, code))
	if err != nil {
		return nil, err
	}
	res[Scene] = sceneMeta

	// 版本meta
	errs := ""
	if r.versions == nil {
		if err := r.InitVersions(code, nil); err != nil {
			errs = err.Error()
		}
	} else {
		if r.versions != nil {
			for _, ver := range r.versions {
				verMeta, err := GetMetaDir(sceneMeta, fmt.Sprintf(`^版本-%s-.*`, ver[1]))
				if err != nil {
					errs += err.Error()
				} else {
					res[ver[1]] = verMeta
				}
			}
		}
	}

	if errs == "" {
		return res, nil
	} else {
		return res, fmt.Errorf("%v", errs)
	}
	// 0为场景，后续为以code为索引的版本
	// errs为获取版本meta时的错误，但有部分正常结果返回
}

func (r *SceneResource) Import(path string) error {
	return postRequest(fmt.Sprintf(`%s:8080/slab-scene/api/inner/scene/import-path?path=%s`, serviceSlabResource, filepath.Join(path, Scene)), "", nil)
	// %s:8080/slab-scene/api/inner/scene-version/sync-relation?path=%s // 版本导入？
}

func (r *SceneResource) Export(codes []string) error {
	return postRequest(fmt.Sprintf(`%s:8080/slab-scene/api/inner/scene/export-path`, serviceSlabResource), filepath.Join(outputTmpDir, Scene), codes)
}

/*** 版本辅助函数 ***/

// InitVersions 初始化场景相关版本信息，覆盖已有
// 优先给定版本codes，否则code查数据库，db非必需
func (r *SceneResource) InitVersions(code string, codes []string) error {
	versions := make([][]string, 0)
	if codes == nil {
		var err error
		versions, err = r.db.QueryMulti(fmt.Sprintf("SELECT v.id, v.`full_code`, v.name FROM %s s JOIN %s v ON s.id = v.scene_id WHERE s.`code` = ?", tableScene, tableSceneVersion), code)
		if err != nil {
			return fmt.Errorf("failed to get scene versions: %v", err)
		}
	} else {
		for _, c := range codes {
			versions = append(versions, []string{"", c, ""})
		}
	}
	r.versions = versions
	return nil
}

// GetSceneByVersion 找版本的场景code
func GetSceneByVersion(db *db.DB, fullCode string) (string, error) {
	return db.QueryRow(fmt.Sprintf("SELECT s.`code` FROM %s v JOIN %s s ON s.id = v.scene_id WHERE v.full_code = ?", tableSceneVersion, tableScene), fullCode)
	// // 或根据full_code和code得出
	// return fullCode[:len(fullCode)-len(code)], nil
}

// GetImageByVersion 找版本id关联的镜像codes
func GetImageByVersion(db *db.DB, id string) ([]string, error) {
	return db.Query(fmt.Sprintf(`SELECT m.code FROM %s s JOIN %s m ON s.mirror_id = m.id WHERE s.source_id = ?`, tableSceneDevice, tableMirror), id)
}
