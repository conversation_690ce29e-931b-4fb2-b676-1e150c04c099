package resource

import (
	"fmt"
	"resource-manager/pkg/db"
)

const Course = "course"

func init() {
	RegisterResource(Course, func(db *db.DB) Resource {
		return &CourseResource{
			db:    db,
			table: "range_edu.courses",
		}
	})
}

type CourseResource struct {
	db    *db.DB
	table string
}

func (r *CourseResource) GetCodes(name string) ([]string, error) {
	return r.db.GetCodesByName(name, r.table)
}

func (r *CourseResource) GetRelatedCodes(code string) (map[string][]string, error) {
	// 无向外关联
	return nil, nil
}

func (r *CourseResource) GetMetaDir(code, path string) (any, error) {
	return "not implemented", nil
}

func (r *CourseResource) Import(path string) error {
	return fmt.Errorf("not implemented, %s", path)
}

func (r *CourseResource) Export(codes []string) error {
	return fmt.Erro<PERSON>("not implemented, %s", outputTmpDir)
}
