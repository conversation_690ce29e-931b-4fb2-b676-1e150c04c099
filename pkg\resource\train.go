package resource

import (
	"fmt"
	"resource-manager/pkg/db"
)

const Train = "train"

func init() {
	RegisterResource(Train, func(db *db.DB) Resource {
		return &TrainResource{
			db:    db,
			table: "slab_train.train",
		}
	})
}

type TrainResource struct {
	db    *db.DB
	table string
}

func (r *TrainResource) GetCodes(name string) ([]string, error) {
	return r.db.GetCodesByName(name, r.table)
}

func (r *TrainResource) GetRelatedCodes(code string) (map[string][]string, error) {
	// 无向外关联
	return nil, nil
}

func (r *TrainResource) GetMetaDir(code, path string) (any, error) {
	return "not implemented", nil
}

func (r *TrainResource) Import(path string) error {
	return fmt.Errorf("not implemented, %s", path)
}

func (r *TrainResource) Export(codes []string) error {
	return fmt.Errorf("not implemented, %s", outputTmpDir)
}
