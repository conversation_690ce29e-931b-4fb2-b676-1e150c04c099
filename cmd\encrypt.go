package cmd

import (
	"archive/tar"
	"bytes"
	"compress/gzip"
	"crypto/md5"
	"crypto/sha256"
	"fmt"
	"io"
	"os"
	"path/filepath"

	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
	"gopkg.in/yaml.v3"

	"maps"
	"resource-manager/internal/info"
	"resource-manager/pkg/crypto"
	"resource-manager/pkg/meta"
	"resource-manager/pkg/resource"
)

const (
	// 加密后文件名
	MetaEnc = "meta.enc"
	InfoEnc = "info.enc"
)

func init() {
	RootCmd.AddCommand(encryptCmd)

	encryptCmd.Flags().StringP("file", "f", "", "file to encrypt")
	encryptCmd.Flags().StringP("key", "k", "", "key to encrypt")

	RootCmd.AddCommand(decryptCmd)
	decryptCmd.Flags().StringP("file", "f", "", "file to decrypt")
	decryptCmd.Flags().StringP("key", "k", "", "key to decrypt")
}

var encryptCmd = &cobra.Command{
	Use:   "encrypt",
	Short: "Encrypt resource package",
	Run: func(cmd *cobra.Command, args []string) {
		file, _ := cmd.Flags().GetString("file")
		keyStr, _ := cmd.Flags().GetString("key")
		if file == "" {
			logrus.Fatal("Please specify file need to encrypt!")
		}

		if keyStr == "" {
			keyStr = "von"
		}

		// 读取info文件
		infoPath := filepath.Join(file, "info")
		rscInfo, err := info.ReadInfo(infoPath)
		if err != nil {
			logrus.Fatalf("%v", err)
		}

		/* 获取meta文件路径集 */
		metaDirs := make(map[string]string) // map[ID]metaDir

		/*** 主体资源 ***/
		// 加载工厂
		mainFct, err := resource.GetFactory(rscInfo.Type, nil)
		if err != nil {
			logrus.Fatalf("failed to get resource: %v", err)
		}
		if err := initResource(mainFct, rscInfo, false); err != nil {
			logrus.Fatalf("failed to init resource: %v", err)
		}
		mainMeta, err := mainFct.GetMetaDir(rscInfo.Code, file)
		// 处理返回值
		if err != nil {
			logrus.Fatalf("resource %s[%s]: %v", rscInfo.Code, rscInfo.Type, err)
		} else if mainMetas, ok := mainMeta.(map[string]string); ok {
			// 为场景及版本，Type=scene
			for code, dir := range mainMetas {
				if code == resource.Scene {
					metaDirs[fmt.Sprintf("%x", md5.Sum([]byte(rscInfo.Code)))] = dir
				} else {
					metaDirs[fmt.Sprintf("%x", md5.Sum([]byte(code)))] = dir
				}
			}
		} else if mainMetaDir, ok := mainMeta.(string); ok {
			metaDirs[fmt.Sprintf("%x", md5.Sum([]byte(rscInfo.Code)))] = mainMetaDir
		} else {
			logrus.Fatal("invalid meta dir type")
		}

		/*** 关联资源 ***/
		if rscInfo.Relation != nil {
			rel := rscInfo.Relation
			// Mirror，默认返回string
			if rel.Mirror != nil {
				fct, err := resource.GetFactory(resource.Mirror, nil)
				if err != nil {
					logrus.Errorf("%v", err)
				} else {
					for _, m := range rel.Mirror {
						dir, err := fct.GetMetaDir(m.Code, file)
						if err != nil {
							logrus.Errorf("resource %s[%s]: %v", m.Code, resource.Mirror, err)
						}
						metaDirs[fmt.Sprintf("%x", md5.Sum([]byte(m.Code)))] = dir.(string)
					}
				}
			}
			// Scene，默认返回map[string][]string
			if rel.Scene != nil {
				fct, err := resource.GetFactory(resource.Scene, nil)
				if err != nil {
					logrus.Errorf("%v", err)
				} else {
					for _, s := range rel.Scene {
						// 初始化versions
						sinfo := &info.Info{
							Type: resource.Scene,
							Code: s.Code,
							Children: map[string][]string{
								resource.Version: s.Children[resource.Version],
							},
						}
						if err := initResource(fct, sinfo, false); err != nil {
							logrus.Errorf("failed to init scene: %v", err)
						}
						// 获取meta，默认返回map[string]string
						dirs, err := fct.GetMetaDir(s.Code, file)
						if err != nil {
							logrus.Errorf("resource %s[%s]: %v", s.Code, resource.Scene, err)
						}
						for code, dir := range dirs.(map[string]string) {
							if code == resource.Scene {
								metaDirs[fmt.Sprintf("%x", md5.Sum([]byte(s.Code)))] = dir
							} else {
								metaDirs[fmt.Sprintf("%x", md5.Sum([]byte(code)))] = dir
							}
						}
					}
				}
			}
			// Tool
			if rel.Tool != nil {
				logrus.Panic("TODO")
			}
			// Knowledge
			if rel.Knowledge != nil {
				logrus.Panic("TODO")
			}
			// Course
			if rel.Course != nil {
				logrus.Panic("TODO")
			}
			// Stem
			if rel.Stem != nil {
				logrus.Panic("TODO")
			}
		}

		/*** 加密 ***/
		// metaPath写入info
		rscInfo.MetaDir = make(map[string]string)
		maps.Copy(rscInfo.MetaDir, metaDirs)
		if err := info.WriteInfo(infoPath, rscInfo); err != nil {
			logrus.Fatalf("failed to update info file: %v", err)
		}

		// 生成加密密钥
		infoKey := crypto.GenerateKey(keyStr)
		infoCtt, err := os.ReadFile(infoPath)
		if err != nil {
			logrus.Fatalf("failed to read info file: %v", err)
		}
		metaKey := crypto.GenerateKey(fmt.Sprintf("%x-%s", sha256.Sum256(infoCtt), keyStr))

		// 打包并加密meta文件
		var metaArchive bytes.Buffer
		gzw := gzip.NewWriter(&metaArchive)
		tw := tar.NewWriter(gzw)

		for id, dir := range metaDirs {
			// 读meta
			ctt, err := os.ReadFile(filepath.Join(dir, meta.Filename))
			if err != nil {
				logrus.Errorf("failed to read meta file %s: %v", dir, err)
				continue
			}

			// 写入tar
			hdr := &tar.Header{
				Name: id,
				Mode: 0600,
				Size: int64(len(ctt)),
			}
			if err := tw.WriteHeader(hdr); err != nil {
				logrus.Errorf("failed to write tar header: %v", err)
				continue
			}
			if _, err := tw.Write(ctt); err != nil {
				logrus.Errorf("failed to write meta content: %v", err)
			}

			// 删除明文meta
			if err := os.Remove(filepath.Join(dir, meta.Filename)); err != nil {
				logrus.Fatalf("failed to remove plain meta: %v", err)
			}
		}
		tw.Close()
		gzw.Close()

		// 加密meta归档
		encryptedMeta, err := crypto.AESEncrypt(metaKey, metaArchive.Bytes())
		if err != nil {
			logrus.Fatalf("failed to encrypt meta archive: %v", err)
		}
		if err := os.WriteFile(filepath.Join(file, MetaEnc), encryptedMeta, 0644); err != nil {
			logrus.Fatalf("failed to write encrypted meta file: %v", err)
		}

		// 加密info
		encryptedInfo, err := crypto.AESEncrypt(infoKey, infoCtt)
		if err != nil {
			logrus.Fatalf("failed to encrypt info: %v", err)
		}
		if err := os.WriteFile(filepath.Join(file, InfoEnc), encryptedInfo, 0644); err != nil {
			logrus.Fatalf("failed to write encrypted info file: %v", err)
		}

		// 删除明文info
		if err := os.Remove(filepath.Join(file, "info")); err != nil {
			logrus.Errorf("failed to remove plain info: %v", err)
		}

		logrus.Info("Encryption completed successfully")
	},
}

// 仅开发测试，实际禁用
var decryptCmd = &cobra.Command{
	Use:   "decrypt",
	Short: "Decrypt resource package",
	Run: func(cmd *cobra.Command, args []string) {
		file, _ := cmd.Flags().GetString("file")
		keyStr, _ := cmd.Flags().GetString("key")

		if file == "" {
			logrus.Fatal("Please specify file need to decrypt!")
		}

		if keyStr == "" {
			keyStr = "von"
		}

		// 解密info
		infoKey := crypto.GenerateKey(keyStr)
		encryptedInfo, err := os.ReadFile(filepath.Join(file, InfoEnc))
		if err != nil {
			logrus.Fatalf("failed to read encrypted info file: %v", err)
		}

		infoBytes, err := crypto.AESDecrypt(infoKey, encryptedInfo)
		if err != nil {
			logrus.Fatalf("failed to decrypt info file: %v", err)
		}

		var rscInfo info.Info
		if err := yaml.Unmarshal(infoBytes, &rscInfo); err != nil {
			logrus.Fatalf("failed to parse info file: %v", err)
		}

		// 生成meta解密密钥
		metaKey := crypto.GenerateKey(fmt.Sprintf("%x-%s", sha256.Sum256(infoBytes), keyStr))

		// 解密meta
		encryptedMeta, err := os.ReadFile(filepath.Join(file, MetaEnc))
		if err != nil {
			logrus.Fatalf("failed to read encrypted meta archive: %v", err)
		}

		decryptedMeta, err := crypto.AESDecrypt(metaKey, encryptedMeta)
		if err != nil {
			logrus.Fatalf("failed to decrypt meta archive: %v", err)
		}

		// 解压meta归档
		gzr, err := gzip.NewReader(bytes.NewReader(decryptedMeta))
		if err != nil {
			logrus.Fatalf("failed to create gzip reader: %v", err)
		}
		tr := tar.NewReader(gzr)

		// 提取并还原meta文件
		for {
			hdr, err := tr.Next()
			if err == io.EOF {
				break
			}
			if err != nil {
				logrus.Errorf("failed to read tar header: %v", err)
				continue
			}

			id := hdr.Name
			metaDir := rscInfo.MetaDir[id]
			if metaDir == "" {
				logrus.Errorf("meta path not found for identifier: %s", id)
				continue
			}

			// 确保目标目录存在
			if err := os.MkdirAll(filepath.Dir(metaDir), 0755); err != nil {
				logrus.Errorf("failed to create meta directory: %v", err)
				continue
			}

			// 写回
			out, err := os.Create(filepath.Join(metaDir, meta.Filename))
			if err != nil {
				logrus.Errorf("failed to create meta file: %v", err)
				continue
			}

			if _, err := io.Copy(out, tr); err != nil {
				logrus.Errorf("failed to write meta content: %v", err)
				out.Close()
				continue
			}
			out.Close()

			logrus.Debugf("restored meta file: %s", metaDir)
		}

		// 写回解密info
		rscInfo.MetaDir = nil
		if err := info.WriteInfo(filepath.Join(file, "info"), &rscInfo); err != nil {
			logrus.Fatalf("failed to write info file: %v", err)
		}

		// 删除密文
		if err := os.Remove(filepath.Join(file, InfoEnc)); err != nil {
			logrus.Errorf("failed to remove encrypted info: %v", err)
		}
		if err := os.Remove(filepath.Join(file, MetaEnc)); err != nil {
			logrus.Errorf("failed to remove encrypted meta: %v", err)
		}

		logrus.Info("Decryption completed successfully")
	},
}
