package resource

import (
	"fmt"
	"resource-manager/pkg/db"
)

const Stem = "stem"

func init() {
	RegisterResource(Stem, func(db *db.DB) Resource {
		return &StemResource{
			db:    db,
			table: "range_exam.stems",
		}
	})
}

type StemResource struct {
	db    *db.DB
	table string
}

func (r *StemResource) GetCodes(name string) ([]string, error) {
	return r.db.GetCodesByName(name, r.table)
}

func (r *StemResource) GetRelatedCodes(code string) (map[string][]string, error) {
	// 无向外关联
	return nil, nil
}

func (r *StemResource) GetMetaDir(code, path string) (any, error) {
	return "not implemented", nil
}

func (r *StemResource) Import(path string) error {
	return fmt.Errorf("not implemented, %s", path)
}

func (r *StemResource) Export(codes []string) error {
	return fmt.Errorf("not implemented, %s", outputTmpDir)
}
