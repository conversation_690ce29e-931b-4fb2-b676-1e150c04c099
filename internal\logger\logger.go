package logger

import (
	"os"
	"strings"

	"github.com/sirupsen/logrus"
)

// levelMap 日志等级
var levelMap = map[string]logrus.Level{
	"PANIC": logrus.PanicLevel,
	"FATAL": logrus.FatalLevel,
	"ERROR": logrus.ErrorLevel,
	"WARN":  logrus.WarnLevel,
	"INFO":  logrus.InfoLevel,
	"DEBUG": logrus.DebugLevel,
	"TRACE": logrus.TraceLevel,
}

// InitLogger 初始化日志输出设置，标准输出
func InitLogger(level string) {
	// 设置日志级别
	logLevel, ok := levelMap[strings.ToUpper(level)]
	if !ok {
		logLevel = logrus.InfoLevel
	}
	logrus.SetLevel(logLevel)

	// 输出格式
	formatter := &logrus.TextFormatter{
		FullTimestamp: true,
		DisableColors: false,
	}

	// 标准输出
	logrus.SetOutput(os.Stdout)
	logrus.SetFormatter(formatter)
}
