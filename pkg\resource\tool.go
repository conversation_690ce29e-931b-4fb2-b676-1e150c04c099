package resource

import (
	"fmt"
	"resource-manager/pkg/db"
)

const Tool = "tool"

func init() {
	RegisterResource(Tool, func(db *db.DB) Resource {
		return &ToolResource{
			db:    db,
			table: "resource_management.tools",
		}
	})
}

type ToolResource struct {
	db    *db.DB
	table string
}

func (r *ToolResource) GetCodes(name string) ([]string, error) {
	return r.db.GetCodesByName(name, r.table)
}

func (r *ToolResource) GetRelatedCodes(code string) (map[string][]string, error) {
	// 无向外关联
	return nil, nil
}

func (r *ToolResource) GetMetaDir(code, path string) (any, error) {
	return "not implemented", nil
}

func (r *ToolResource) Import(path string) error {
	return fmt.Errorf("not implemented, %s", path)
}

func (r *ToolResource) Export(codes []string) error {
	return fmt.Errorf("not implemented, %s", outputTmpDir)
}
